import factory
from factory import BUILD_STRATEGY

from apps.assets.models import AssetCriticality, HostAssetAttributes, InternetExposure
from apps.assets.models.source_host import (
    HostAttributes,
    IntegrationMeta,
    MergedAssetMeta,
    SourceHost,
)
from apps.integrations.models import Integration
from apps.integrations.utils import get_external_technology_ids, get_technology_category
from factories.os_attributes import OsAttributesFactory
from factories.owner_attributes import OwnerAttributesFactory


class HostAttributesFactory(factory.Factory):
    class Meta:
        strategy = BUILD_STRATEGY
        model = HostAttributes
        exclude = ("domain",)

    criticality = factory.Faker(
        provider="random_element",
        elements=list(AssetCriticality),
    )
    last_seen = factory.Faker(
        provider="date_time_between",
        start_date="-1M",
        end_date="now",
    )

    hostname = factory.Faker(provider="hostname", levels=0)
    domain = factory.Faker(provider="domain_name", levels=2)
    fqdns = factory.LazyAttribute(lambda o: [f"{o.hostname}.{o.domain}"])

    os = factory.SubFactory(OsAttributesFactory)

    public_ips = factory.List([factory.Faker(provider="ipv4_public")])
    private_ips = factory.List(
        [
            factory.Faker(provider="ipv4_private"),
            factory.Faker(provider="ipv4_private"),
        ]
    )
    universal_mac_addresses = factory.List(
        [
            factory.Faker(provider="mac_address"),
            factory.Faker(provider="mac_address"),
        ]
    )
    local_mac_addresses = factory.List(
        [
            factory.Faker(provider="mac_address"),
            factory.Faker(provider="mac_address"),
        ]
    )
    internet_exposure = factory.Faker(
        provider="random_element",
        elements=list(InternetExposure),
    )

    owners = factory.List([factory.SubFactory(OwnerAttributesFactory)])
    group_names = factory.List(
        [
            factory.Faker(provider="word", part_of_speech="noun"),
            factory.Faker(provider="word", part_of_speech="noun"),
        ]
    )

    aad_id = factory.Faker(provider="uuid4")

    @classmethod
    def _build(cls, model_class, *args, **kwargs):
        if "primary_ip_address" not in kwargs:
            primary = HostAssetAttributes.default_primary_ip_addr(
                preferred_ips=kwargs.get("private_ips"),
                secondary_ips=kwargs.get("public_ips"),
            )
            kwargs["primary_ip_address"] = primary
        if "primary_mac_address" not in kwargs:
            primary = HostAssetAttributes.default_primary_mac_addr(
                universal_addresses=kwargs.get("universal_mac_addresses"),
                local_addresses=kwargs.get("local_mac_addresses"),
            )
            kwargs["primary_mac_address"] = primary
        return super()._build(model_class, *args, **kwargs)

    @classmethod
    def _create(cls, model_class, *args, **kwargs):
        return cls._build(model_class, *args, **kwargs)


class IntegrationMetaFactory(factory.Factory):
    class Meta:
        strategy = BUILD_STRATEGY
        model = IntegrationMeta

    id = factory.Faker(provider="uuid4")
    organization_id = factory.Faker(provider="uuid4")
    technology_id = factory.LazyFunction(
        lambda: get_external_technology_ids()[0]
        if get_external_technology_ids()
        else "test_tech"
    )
    vulnerability_coverage_mode = Integration.CoverageMode.NOT_APPLICABLE
    endpoint_coverage_mode = Integration.CoverageMode.NOT_APPLICABLE

    @factory.lazy_attribute
    def category_id(self):  # pragma: no cover
        # Use correct category based on technology_id
        return get_technology_category(self.technology_id)

    @classmethod
    def from_model(cls, integration):
        return cls(
            id=integration.id,
            organization_id=integration.organization_id,
            technology_id=integration.technology_id,
            category_id=integration.category_id,
            vulnerability_coverage_mode=integration.vulnerability_coverage_mode,
            endpoint_coverage_mode=integration.endpoint_coverage_mode,
        )


class MergedAssetMetaFactory(factory.Factory):
    class Meta:
        strategy = BUILD_STRATEGY
        model = MergedAssetMeta

    id = factory.Faker(provider="uuid4")
    manually_merged = False


class SourceHostFactory(factory.Factory):
    class Meta:
        model = SourceHost
        strategy = BUILD_STRATEGY

    source_id = factory.Faker(provider="password", length=15, special_chars=False)

    attributes = factory.SubFactory(HostAttributesFactory)
    integration = factory.SubFactory(IntegrationMetaFactory)
    merged_asset = factory.SubFactory(MergedAssetMetaFactory)

    @classmethod
    def _create(cls, model_class, *args, **kwargs):
        merged_asset = cls._build(model_class, *args, **kwargs)
        SourceHost.documents.create_bulk([merged_asset])
        return merged_asset

    @classmethod
    def create_batch(cls, size, **kwargs):
        merged_assets = cls.build_batch(size, **kwargs)
        SourceHost.documents.create_bulk(merged_assets)
        return merged_assets
