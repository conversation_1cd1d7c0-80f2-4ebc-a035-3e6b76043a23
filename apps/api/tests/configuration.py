from apps.assets.tests.es_base import ESCaseMixin
from apps.integrations.api.data_connectors import CoverageMode
from apps.integrations.models.integration import Integration
from apps.tests.base import ApiDatabaseTestCase
from factories.integration import IntegrationFactory
from factories.merged_host import MergedHostFactory
from factories.merged_source_host import MergedSourceHostFactory


class ConfigurationStateTests(ESCaseMixin, ApiDatabaseTestCase):
    fixtures = ["accounts.json"]

    def setUp(self):
        super().setUp()
        self.patch("apps.api.entitlements.check_entitlement")

    def test_no_integrations(self):
        response = self.get()
        self.assertEqual(200, response.status_code)
        state = response.json()
        self.assertIn("service_integrations", state)
        self.assertEqual(0, len(state["service_integrations"]))

    def test_asset_source_has_data(self):
        integration_1 = self.create_integration("defender_atp", "endpoint_security")
        integration_2 = self.create_integration(
            "crowdstrike_falcon", "endpoint_security"
        )

        # create a merged asset with source assets from integration_1
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata__integration__id=integration_1.id,
                    metadata__integration__technology_id=integration_1.technology_id,
                    attributes__hostname="host1",
                ),
                MergedSourceHostFactory(
                    metadata__integration__id=integration_1.id,
                    metadata__integration__technology_id=integration_1.technology_id,
                    attributes__hostname="host1",
                ),
            ],
        )

        response = self.get()
        self.assertEqual(200, response.status_code)
        state = response.json()
        self.assertIn("service_integrations", state)
        self.assertEqual(2, len(state["service_integrations"]))
        self.assertEqual(str(integration_1.id), state["service_integrations"][0]["id"])
        self.assertEqual(
            integration_1.technology_id,
            state["service_integrations"][0]["technology_id"],
        )
        self.assertTrue(state["service_integrations"][0]["has_data"])
        self.assertEqual(str(integration_2.id), state["service_integrations"][1]["id"])
        self.assertEqual(
            integration_2.technology_id,
            state["service_integrations"][1]["technology_id"],
        )
        self.assertFalse(state["service_integrations"][1]["has_data"])

    def test_endpoint_security_coverage_has_eligible_sources_false(self):
        self.assert_coverage("endpoint_coverage", "has_eligible_asset_sources", False)

    def test_endpoint_security_coverage_has_eligible_sources_true(self):
        self.create_integration(
            "defender_atp",
            "endpoint_security",
            CoverageMode.ENABLED,
            endpoint_coverage_mode=CoverageMode.ENABLED,
        )
        self.assert_coverage("endpoint_coverage", "has_eligible_asset_sources", True)

    def test_vulnerability_management_coverage_has_eligible_sources_false(self):
        self.assert_coverage(
            "vulnerability_coverage", "has_eligible_asset_sources", False
        )

    def test_vulnerability_management_coverage_has_eligible_sources_true(self):
        self.create_integration(
            "tenable_io", coverage_mode=Integration.CoverageMode.ENABLED
        )
        self.assert_coverage(
            "vulnerability_coverage", "has_eligible_asset_sources", True
        )

    def test_endpoint_security_coverage_has_designated_sources_false(self):
        self.assert_coverage("endpoint_coverage", "has_designated_asset_sources", False)

    def test_endpoint_security_coverage_has_designated_sources_true(self):
        self.create_integration(
            "defender_atp",
            "endpoint_security",
            CoverageMode.ENABLED,
            endpoint_coverage_mode=CoverageMode.ENABLED,
        )
        self.assert_coverage("endpoint_coverage", "has_designated_asset_sources", True)

    def test_vulnerability_management_coverage_has_designated_sources_false(self):
        self.assert_coverage(
            "vulnerability_coverage", "has_designated_asset_sources", False
        )

    def test_vulnerability_management_coverage_has_designated_sources_true(self):
        self.create_integration(
            "tenable_io", coverage_mode=Integration.CoverageMode.ENABLED
        )
        self.assert_coverage(
            "vulnerability_coverage", "has_designated_asset_sources", True
        )

    def test_endpoint_security_coverage_has_additional_sources_false(self):
        self.create_integration("defender_atp", "endpoint_security")
        self.assert_coverage("endpoint_coverage", "has_additional_asset_sources", False)

    def test_endpoint_security_coverage_has_additional_sources_true(self):
        self.create_integration("defender_atp", "endpoint_security")
        self.create_integration("user_input", "endpoint_security")
        self.assert_coverage("endpoint_coverage", "has_additional_asset_sources", True)

    def test_vulnerability_management_coverage_has_additional_sources_false(self):
        self.assert_coverage(
            "vulnerability_coverage", "has_additional_asset_sources", False
        )

    def test_vulnerability_management_coverage_has_additional_sources_true(self):
        self.create_integration(
            "tenable_io", coverage_mode=Integration.CoverageMode.ENABLED
        )
        self.create_integration(
            "user_input", coverage_mode=Integration.CoverageMode.ENABLED
        )
        self.assert_coverage(
            "vulnerability_coverage", "has_additional_asset_sources", True
        )

    def create_integration(
        self,
        technology_id,
        category_id="vulnerability_management",
        coverage_mode=CoverageMode.NOT_APPLICABLE,
        enabled=True,
        endpoint_coverage_mode=CoverageMode.NOT_APPLICABLE,
    ):
        return IntegrationFactory.create(
            organization=self.organization,
            technology_id=technology_id,
            category_id=category_id,
            vulnerability_coverage_mode=coverage_mode,
            enabled=enabled,
            endpoint_coverage_mode=endpoint_coverage_mode,
        )

    def assert_coverage(self, coverage_type, coverage_state_key, expected_value):
        response = self.get()
        self.assertEqual(200, response.status_code)
        state = response.json()
        self.assertIn(coverage_type, state)
        self.assertEqual(expected_value, state[coverage_type][coverage_state_key])

    def get(self, status_code=200, query=None, params=None):
        params = params or {}
        path = "/api/v1/configuration/state"

        response = self.api_client.get(path, params=params)
        self.assertEqual(status_code, response.status_code, response.json())
        return response
