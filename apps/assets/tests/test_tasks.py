import unittest
import unittest.mock
from io import BytesIO
from unittest.mock import MagicMock, patch

from django.core.management import call_command
from django.test import TestCase, override_settings
from django.utils import timezone

from apps.assets.models import (
    AssetType,
    IdentificationRule,
    IdentifierEntry,
    MergedAsset,
    ReconcilerEntry,
    ReconciliationRule,
    SourceHost,
    SyncTask,
)
from apps.assets.models.asset_field import (
    AssetField,
)
from apps.assets.tasks import (
    delete_assets_for_integration,
    delete_old_sync_tasks,
    get_result_content,
    recalculate_mac_address,
    update_merged_asset_ids,
    update_source_assets,
)
from apps.assets.tests.es_base import ESCaseMixin
from apps.tests.base import BaseTestCase
from factories.integration import IntegrationFactory
from factories.merged_host import MergedHostFactory
from factories.merged_source_host import MergedSourceHostFactory
from factories.staged_host import StagedHostFactory
from factories.sync_task import SyncTaskFactory


@override_settings(CELERY_TASK_ALWAYS_EAGER=True, CELERY_TASK_EAGER_PROPAGATES=False)
class OldSyncTaskTestCase(TestCase):
    def test_delete_old_sync_tasks(self):
        integration = IntegrationFactory.create()
        new_sync_tasks = SyncTaskFactory.create_batch(
            2,
            integration=integration,
        )
        SyncTaskFactory.create_batch(
            2,
            integration=integration,
            finished_at=timezone.now() - timezone.timedelta(days=30),
        )

        self.assertTrue(SyncTask.objects.count(), 4)

        delete_old_sync_tasks.delay()

        self.assertTrue(SyncTask.objects.count(), 2)
        self.assertTrue(
            SyncTask.objects.filter(id__in=[t.id for t in new_sync_tasks]).count(), 2
        )


class TestGetResultContent(TestCase):
    @patch("apps.assets.tasks.use_fs")
    def test_get_result_content_single_file(self, mock_use_fs):
        mock_fs = MagicMock()
        mock_use_fs.return_value = mock_fs

        mock_buffer = BytesIO(b"test_content")
        mock_fs.read_file.side_effect = (
            lambda result_id, buffer: buffer.write(mock_buffer.getvalue()) or None
        )

        result_ids = ["file1"]
        result = get_result_content(result_ids, unittest.mock.ANY)

        self.assertEqual(result, b"test_content")
        mock_fs.read_file.assert_called_once_with("file1", unittest.mock.ANY)

    @patch("apps.assets.tasks.use_fs")
    def test_get_result_content_multiple_files(self, mock_use_fs):
        mock_fs = MagicMock()
        mock_use_fs.return_value = mock_fs

        file_contents = [b"test_content_1", b"test_content_2"]
        mock_fs.read_file.side_effect = (
            lambda result_id, buffer: buffer.write(file_contents.pop(0)) or None
        )

        result_ids = ["file1", "file2"]
        result = get_result_content(result_ids, unittest.mock.ANY)

        self.assertEqual(result, b"test_content_1\ntest_content_2")
        self.assertEqual(mock_fs.read_file.call_count, 2)

    @patch("apps.assets.tasks.use_fs")
    def test_get_result_content_handles_empty_result_ids(self, mock_use_fs):
        mock_fs = MagicMock()
        mock_use_fs.return_value = mock_fs

        result_ids = []
        result = get_result_content(result_ids, unittest.mock.ANY)

        self.assertEqual(result, b"")
        mock_fs.read_file.assert_not_called()


class RecalculateFieldsTestCase(ESCaseMixin, BaseTestCase):
    def setUp(self):
        super().setUp()

        self.excluded_value = "00:50:56:C0:00:0"
        AssetField.objects.create(
            name="universal_mac_addresses",
            asset_type=AssetType.HOST,
            excluded_values=[self.excluded_value],
        )

        self.identification_rule = IdentificationRule.objects.create(
            asset_type=AssetType.HOST,
        )
        self.reconciliation_rule = ReconciliationRule.objects.create(
            asset_type=AssetType.HOST,
            field="primary_mac_address",
        )

        self.integration = IntegrationFactory.create(
            technology_id="crowdstrike_falcon",
            organization=self.organization,
        )
        ReconcilerEntry.objects.create(
            reconciliation_rule=self.reconciliation_rule,
            priority=1,
            technology_id="crowdstrike_falcon",
        )

    def test_recalculate_mac_address(self):
        new_address = "fc:77:74:c0:26:f0"
        merged_asset = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata__integration__id=self.integration.id,
                    metadata__integration__technology_id=self.integration.technology_id,
                    attributes__primary_mac_address=self.excluded_value,
                    attributes__universal_mac_address=[
                        self.excluded_value,
                        new_address,
                    ],
                )
            ],
        )
        self.assertNotEqual(merged_asset.merged_data.primary_mac_address, new_address)

        recalculate_mac_address()

        merged_asset = MergedAsset.documents.search({})[0]
        self.assertEqual(merged_asset.merged_data.primary_mac_address, new_address)


@override_settings(CELERY_TASK_ALWAYS_EAGER=True)
class UpdateMergedAssetIdsTestCase(ESCaseMixin, BaseTestCase):
    def test_update_merged_asset_ids_no_assets(self):
        call_command("sync_merging_rules")
        integration = IntegrationFactory.create()
        sync_task = SyncTaskFactory.create(
            integration=integration,
            status=SyncTask.Status.STAGED,
        )

        update_merged_asset_ids.delay(
            correlation_id=sync_task.correlation_id,
            organization_id=self.organization.id,
            sync_type="Host",
        )
        self.assertTrue(True, "ensure the task runs without errors")


@override_settings(CELERY_TASK_ALWAYS_EAGER=True)
class DCIntegrationDeletedTestCase(ESCaseMixin, BaseTestCase):
    def setUp(self):
        super().setUp()
        call_command("sync_merging_rules")

        self.integration_1 = IntegrationFactory.create(
            id="898890c3-21c2-4b35-b069-983a0b5060ed",
            organization=self.organization,
            technology_id="defender_atp",
        )
        self.integration_2 = IntegrationFactory.create(
            id="de62f69a-c1d3-4031-a781-45ea5b431fb6",
            organization=self.organization,
            technology_id="qualys_vmpc",
        )
        self.identification_rule = IdentificationRule.objects.create(
            asset_type="host",
            organization=self.organization,
        )
        self.identification_entry = IdentifierEntry.objects.create(
            identification_rule=self.identification_rule,
            fields=["hostname"],
        )

    def test_source_assets_deleted_when_integration_deleted(self):
        merged_asset_1 = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata__integration__id=self.integration_1.id,
                    metadata__integration__technology_id=self.integration_1.technology_id,
                    attributes__hostname="host1",
                    attributes__universal_mac_address=["f0:20:ff:5a:75:f8"],
                ),
                MergedSourceHostFactory(
                    metadata__integration__id=self.integration_1.id,
                    metadata__integration__technology_id=self.integration_1.technology_id,
                    attributes__hostname="host1",
                    attributes__universal_mac_address=["f0:20:ff:5a:75:f8"],
                ),
                MergedSourceHostFactory(
                    metadata__integration__id=self.integration_2.id,
                    metadata__integration__technology_id=self.integration_2.technology_id,
                    attributes__hostname="host1",
                    attributes__universal_mac_address=["f0:20:ff:5a:75:f8"],
                ),
            ],
        )

        merged_asset_2 = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata__integration__id=self.integration_1.id,
                    metadata__integration__technology_id=self.integration_1.technology_id,
                    attributes__hostname="host2",
                    attributes__universal_mac_address=["f0:20:ff:5a:75:f7"],
                ),
                MergedSourceHostFactory(
                    metadata__integration__id=self.integration_2.id,
                    metadata__integration__technology_id=self.integration_2.technology_id,
                    attributes__hostname="host2",
                    attributes__universal_mac_address=["f0:20:ff:5a:75:f7"],
                ),
                MergedSourceHostFactory(
                    metadata__integration__id=self.integration_2.id,
                    metadata__integration__technology_id=self.integration_2.technology_id,
                    attributes__hostname="host2",
                    attributes__universal_mac_address=["f0:20:ff:5a:75:f7"],
                ),
            ],
        )

        self.assertEqual(merged_asset_1.source_data.count(), 3)
        self.assertEqual(merged_asset_2.source_data.count(), 3)

        self.assertEqual(len(SourceHost.documents.search({})), 6)

        delete_assets_for_integration(self.integration_2.id)

        ma_1 = MergedAsset.documents.get_by_id(merged_asset_1.id)
        self.assertEqual(ma_1.source_data.count(), 2)

        ma_2 = MergedAsset.documents.get_by_id(merged_asset_2.id)
        self.assertEqual(ma_2.source_data.count(), 1)

    def test_merged_asset_is_deleted_when_integration_deleted(self):
        merged_asset_1 = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata__integration__id=self.integration_1.id,
                    metadata__integration__technology_id=self.integration_1.technology_id,
                ),
                MergedSourceHostFactory(
                    metadata__integration__id=self.integration_1.id,
                    metadata__integration__technology_id=self.integration_1.technology_id,
                ),
            ],
        )

        self.assertEqual(merged_asset_1.source_data.count(), 2)

        delete_assets_for_integration(self.integration_1.id)

        ma_1 = MergedAsset.documents.get_by_id(merged_asset_1.id)
        self.assertIsNone(ma_1)

    def test_merged_asset_without_deleted_integration_is_not_deleted(self):
        merged_asset_1 = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata__integration__id=self.integration_1.id,
                    metadata__integration__technology_id=self.integration_1.technology_id,
                ),
            ],
        )

        self.assertEqual(merged_asset_1.source_data.count(), 1)

        delete_assets_for_integration(self.integration_2.id)

        ma_1 = MergedAsset.documents.get_by_id(merged_asset_1.id)
        self.assertEqual(ma_1.source_data.count(), 1)

    @patch("apps.assets.tasks.merge_service.remove_sources_for_integration")
    def test_delete_assets_for_integration_integration_not_found(
        self, mock_remove_sources
    ):
        non_existent_id = "00000000-0000-0000-0000-000000000000"
        with self.assertLogs("apps.assets.tasks", level="INFO") as log:
            delete_assets_for_integration(non_existent_id)
            self.assertIn(
                f"Integration {non_existent_id} not found.  Nothing to delete.",
                log.output[0],
            )
        mock_remove_sources.assert_not_called()


@override_settings(CELERY_TASK_ALWAYS_EAGER=True)
class MergeAssetsTaskTestCase(ESCaseMixin, TestCase):
    @patch("apps.assets.tasks.integration_response_staging.read_raw")
    @patch("apps.assets.services.merging.merge_service.advisory_lock")
    def test_merge_assets_lock_retry(self, m_advisory_lock, m_read_raw):
        call_command("sync_merging_rules")

        integration = IntegrationFactory.create()
        sync_task = SyncTaskFactory.create(
            integration=integration,
            status=SyncTask.Status.STAGED,
        )
        staged_host = StagedHostFactory(hostname="A", last_seen="2021-01-01T00:00:00Z")
        staged_hosts = [staged_host]
        m_read_raw.return_value = staged_hosts

        # Simulate the lock being held initially
        mock_lock = MagicMock()
        mock_lock.__enter__.side_effect = [False, True, False, True]
        m_advisory_lock.return_value = mock_lock

        update_source_assets.delay(
            correlation_id=sync_task.correlation_id,
            staging_key="test_staging_key",
            return_type="Host",
        )
        merged_assets = list(MergedAsset.documents.search({}))
        self.assertEqual(1, len(merged_assets))

    @patch("apps.assets.tasks.integration_response_staging.read_raw")
    @patch("apps.assets.models.merged_asset.es_service.get_seq_no")
    def test_merge_asset_invalid_seq_no(self, m_get_seq_no, m_read_raw):
        call_command("sync_merging_rules")

        integration = IntegrationFactory.create()
        sync_task = SyncTaskFactory.create(
            integration=integration,
            status=SyncTask.Status.STAGED,
        )
        staged_host = StagedHostFactory(hostname="A", last_seen="2021-01-01T00:00:00Z")
        staged_hosts = [staged_host]
        m_read_raw.return_value = staged_hosts

        update_source_assets.delay(
            correlation_id=sync_task.correlation_id,
            staging_key="test_staging_key",
            return_type="Host",
        )
        merged_assets = list(MergedAsset.documents.search({}))
        previous_updated = merged_assets[0].metadata.updated
        self.assertEqual(1, len(merged_assets))

        # Modify and update the merged asset
        staged_host["last_seen"] = "2021-01-01T00:00:01Z"
        staged_hosts = [staged_host]
        m_read_raw.return_value = staged_hosts

        # Simulate the seq_no being invalid initially
        call_count = 0

        def seq_no(doc):
            nonlocal call_count
            if call_count == 0:
                call_count += 1
                return 1000
            return doc.get("_seq_no")

        m_get_seq_no.side_effect = seq_no

        update_source_assets.delay(
            correlation_id=sync_task.correlation_id,
            staging_key="test_staging_key",
            return_type="Host",
        )

        merged_assets = list(MergedAsset.documents.search({}))
        self.assertEqual(1, len(merged_assets))
        self.assertLess(previous_updated, merged_assets[0].metadata.updated)
