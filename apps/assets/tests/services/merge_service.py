import logging
from datetime import datetime

from ata_common.utils.hash import hash_dict
from django.core.management import call_command

from apps.assets.models import (
    AssetCriticality,
    AssetType,
    CoverageCategory,
    MergedAsset,
    OsFamily,
    ReconciliationRule,
    Setting,
    SettingKey,
)
from apps.assets.services import merge_service
from apps.assets.tests.es_base import ESCaseMixin
from apps.tests.base import BaseTestCase
from factories.integration import IntegrationFactory
from factories.merged_host import MergedHostFactory
from factories.merged_source_host import (
    HostAttributesFactory,
    MergedSourceHostFactory,
    SourceHostMetadataFactory,
)
from factories.setting import SettingFactory
from factories.staged_host import StagedHostFactory


class MergeServiceTestCase(ESCaseMixin, BaseTestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.set_logging(True)
        call_command("sync_merging_rules")

    def test_merge_merged_assets(self):
        integrations = {
            technology_id: IntegrationFactory(
                technology_id=technology_id, organization=self.organization
            )
            for technology_id in [
                "ms_intune",
                "azure_ad",
                "defender_atp",
                "qualys_vmpc",
            ]
        }

        def asset_id(technology_id):
            return hash_dict(
                {
                    "organization_id": integrations[technology_id].organization.id,
                    "technology_id": technology_id,
                    "source_id": f"{technology_id}_source_id",
                }
            )

        ms_intune_source = MergedSourceHostFactory(
            asset_id=asset_id("ms_intune"),
            metadata__integration__id=integrations["ms_intune"].id,
            metadata__integration__technology_id="ms_intune",
            metadata__asset=SourceHostMetadataFactory(
                source_id="ms_intune_source_id",
                last_seen=datetime(2021, 1, 1),
            ),
            attributes=HostAttributesFactory(
                hostname="cs_in-testing",
                fqdn=[],
                local_mac_address=[],
                universal_mac_address=["f0:20:ff:5a:75:f8"],
                aad_id="ab34-AAD_ID",
            ),
        )
        azure_ad_source = MergedSourceHostFactory(
            asset_id=asset_id("azure_ad"),
            metadata__integration__id=integrations["azure_ad"].id,
            metadata__integration__technology_id="azure_ad",
            metadata__asset=SourceHostMetadataFactory(
                source_id="azure_ad_source_id",
                last_seen=datetime(2021, 1, 1),
            ),
            attributes=HostAttributesFactory(
                hostname="cs_in-testing",
                fqdn=[],
                local_mac_address=[],
                universal_mac_address=[],
                aad_id="ab34-AAD_ID",
            ),
        )

        defender_atp_source = MergedSourceHostFactory(
            asset_id=asset_id("defender_atp"),
            metadata__integration__id=integrations["defender_atp"].id,
            metadata__integration__technology_id="defender_atp",
            metadata__asset=SourceHostMetadataFactory(
                source_id="defender_atp_source_id",
                last_seen=datetime(2021, 1, 1),
            ),
            attributes=HostAttributesFactory(
                hostname="cs_in-testing",
                fqdn=["cs_in-testing"],
                local_mac_address=["f2:20:ff:5a:75:f8", "f2:20:ff:5a:75:f8"],
                universal_mac_address=["f0:20:ff:5a:75:f9", "f0:20:ff:5a:75:f8"],
                aad_id=None,
            ),
        )

        qualys_vmpc_source = MergedSourceHostFactory(
            asset_id=asset_id("qualys_vmpc"),
            metadata__integration__id=integrations["qualys_vmpc"].id,
            metadata__integration__technology_id="qualys_vmpc",
            metadata__asset=SourceHostMetadataFactory(
                source_id="qualys_vmpc_source_id",
                last_seen=datetime(2021, 1, 1),
            ),
            attributes=HostAttributesFactory(
                hostname="cs_in-testing",
                fqdn=[],
                local_mac_address=[],
                universal_mac_address=[],
                aad_id=None,
            ),
        )

        merged_host1 = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[ms_intune_source, azure_ad_source],
        )

        merged_host2 = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[defender_atp_source, qualys_vmpc_source],
        )

        rec_rules = [
            (rule, list(rule.entries.all()))
            for rule in ReconciliationRule.objects.filter(
                asset_type=AssetType.HOST, organization=None
            )
        ]

        for mh in [merged_host1, merged_host2]:
            mh.reconcile(rec_rules)
            MergedAsset.documents.update(mh)

        def get_staged_host(source):
            return StagedHostFactory(
                source_id=source.metadata.asset.source_id,
                hostname=source.attributes.hostname,
                fqdns=source.attributes.fqdn,
                mac_addresses=source.attributes.local_mac_address
                + source.attributes.universal_mac_address,
                aad_id=source.attributes.aad_id,
                last_seen=datetime.now(),
            )

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 2, "Two merged assets should exist")

        source_map = {
            "azure_ad": azure_ad_source,
            "defender_atp": defender_atp_source,
            "ms_intune": ms_intune_source,
            "qualys_vmpc": qualys_vmpc_source,
        }

        technology_id = "azure_ad"
        merge_service.merge_staged_assets(
            integrations[technology_id],
            [get_staged_host(source_map[technology_id])],
            "Host",
        )

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 1, "One merged asset should exist")
        self.assertEqual(
            merged_assets[0].source_data.count(), 4, "All sources should be present"
        )

    def test_try_realign_sources_match(self):
        integrations = {
            technology_id: IntegrationFactory(
                technology_id=technology_id, organization=self.organization
            )
            for technology_id in [
                "ms_intune",
                "azure_ad",
            ]
        }

        def asset_id(technology_id):
            return hash_dict(
                {
                    "organization_id": integrations[technology_id].organization.id,
                    "technology_id": technology_id,
                    "source_id": f"{technology_id}_source_id",
                }
            )

        ms_intune_source = MergedSourceHostFactory(
            asset_id=asset_id("ms_intune"),
            metadata__integration__id=integrations["ms_intune"].id,
            metadata__integration__technology_id="ms_intune",
            metadata__asset=SourceHostMetadataFactory(
                source_id="ms_intune_source_id",
                last_seen=datetime(2021, 1, 1),
            ),
            attributes=HostAttributesFactory(
                hostname="cs_in-testing",
                fqdn=[],
                local_mac_address=[],
                universal_mac_address=["f0:20:ff:5a:75:f8"],
                aad_id="ab34-AAD_ID",
            ),
        )
        azure_ad_source = MergedSourceHostFactory(
            asset_id=asset_id("azure_ad"),
            metadata__integration__id=integrations["azure_ad"].id,
            metadata__integration__technology_id="azure_ad",
            metadata__asset=SourceHostMetadataFactory(
                source_id="azure_ad_source_id",
                last_seen=datetime(2021, 1, 1),
            ),
            attributes=HostAttributesFactory(
                hostname="cs_in-testing",
                fqdn=[],
                local_mac_address=[],
                universal_mac_address=[],
                aad_id="ab34-AAD_ID",
            ),
        )

        rec_rules = [
            (rule, list(rule.entries.all()))
            for rule in ReconciliationRule.objects.filter(
                asset_type=AssetType.HOST, organization=None
            )
        ]

        merged_host = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[ms_intune_source, azure_ad_source],
        )

        merged_host.reconcile(rec_rules)
        MergedAsset.documents.update(merged_host)

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 1)
        self.assertEqual("cs_in-testing", merged_assets[0].merged_data.hostname)

        def get_staged_host(source):
            return StagedHostFactory(
                source_id=source.metadata.asset.source_id,
                hostname="changed_hostname",
                fqdns=source.attributes.fqdn,
                mac_addresses=source.attributes.local_mac_address
                + source.attributes.universal_mac_address,
                aad_id=source.attributes.aad_id,
                last_seen=datetime.now(),
            )

        merge_service.merge_staged_assets(
            integrations["ms_intune"],
            [get_staged_host(ms_intune_source)],
            "Host",
        )

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 1, "One merged asset should exist")
        self.assertEqual(merged_assets[0].source_data.count(), 2)
        self.assertEqual("changed_hostname", merged_assets[0].merged_data.hostname)

    def test_merged_asset_created_and_deleted(self):
        """
        The MergeHelper tracks the operation to be performed on the merged asset
        during the merge process. If a merged asset is created and then deleted
        in the same merge operation, the MergeHelper should ignore both the creation
        and deletion operations.

        In the setup below, the merge process will create a merged asset for #1 and
        #2, and then delete on of them when processing #3 matches both #1 and #2.
        """
        integration = IntegrationFactory(organization=self.organization)
        staged_host1 = StagedHostFactory(
            hostname="host1", mac_addresses=["f0:20:ff:5a:75:f8"]
        )
        staged_host2 = StagedHostFactory(
            hostname="host2", mac_addresses=["f0:20:ff:5a:75:f7"]
        )
        staged_host3 = StagedHostFactory(
            hostname="host2", mac_addresses=["f0:20:ff:5a:75:f8", "f0:20:ff:5a:75:f7"]
        )

        merged_asset = self.merge_staged_assets(
            integration, [staged_host1, staged_host2, staged_host3], 1
        )
        self.assertEqual(3, merged_asset.source_data.count())

    def test_auto_assign_criticality(self):
        integration = IntegrationFactory(organization=self.organization)
        SettingFactory.create(
            category=Setting.Category.ASSET_CRITICALITY,
            key=SettingKey.OS_MAC,
            default_value={"enabled": True, "criticality": AssetCriticality.TIER_1},
        )
        staged_host = StagedHostFactory(
            hostname="host1",
            criticality=AssetCriticality.UNKNOWN,
            os__family=OsFamily.MAC,
        )
        merged_asset = self.merge_staged_assets(integration, [staged_host], 1)

        self.assert_auto_criticality(merged_asset, AssetCriticality.TIER_1)

    def test_auto_assign_criticality_unchanged_asset(self):
        # First merge without any criticality settings
        integration = IntegrationFactory(organization=self.organization)
        staged_host = StagedHostFactory(
            hostname="host1",
            criticality=AssetCriticality.UNKNOWN,
            os__family=OsFamily.MAC,
        )
        merged_asset = self.merge_staged_assets(integration, [staged_host], 1)

        self.assert_auto_criticality(merged_asset, AssetCriticality.UNKNOWN)
        #######################################################################

        # Second merge the same asset again (unchanged) with criticality settings
        setting = SettingFactory.create(
            category=Setting.Category.ASSET_CRITICALITY,
            key=SettingKey.OS_MAC,
            default_value={"enabled": True, "criticality": AssetCriticality.TIER_1},
        )
        merged_asset = self.merge_staged_assets(integration, [staged_host], 1)

        self.assert_auto_criticality(merged_asset, AssetCriticality.TIER_1)
        #######################################################################

        # Third merge the same asset again (unchanged) with different criticality settings
        setting.default_value = {
            "enabled": True,
            "criticality": AssetCriticality.TIER_2,
        }
        setting.save()
        merged_asset = self.merge_staged_assets(integration, [staged_host], 1)

        self.assert_auto_criticality(merged_asset, AssetCriticality.TIER_2)

    def test_auto_assign_criticality_revert_to_source_criticality(self):
        crowdstrike_integration = IntegrationFactory(
            organization=self.organization, technology_id="crowdstrike_falcon"
        )
        SettingFactory.create(
            category=Setting.Category.ASSET_CRITICALITY,
            key=SettingKey.OS_MAC,
            default_value={"enabled": True, "criticality": AssetCriticality.TIER_4},
        )
        staged_host = StagedHostFactory(
            hostname="host1",
            criticality=AssetCriticality.UNKNOWN,
            mac_addresses=[
                "f0:20:ff:5a:75:f8"
            ],  # set to trigger merge with second asset
            os__family=OsFamily.MAC,
        )
        merged_asset = self.merge_staged_assets(
            crowdstrike_integration, [staged_host], 1
        )

        self.assert_auto_criticality(merged_asset, AssetCriticality.TIER_4)
        #######################################################################

        # Second merge an asset from a different source with a criticality
        qualys_integration = IntegrationFactory(
            organization=self.organization, technology_id="qualys_vmpc"
        )
        staged_host = StagedHostFactory(
            hostname="host1",
            criticality=AssetCriticality.TIER_2,
            mac_addresses=["f0:20:ff:5a:75:f8"],
            os__family=OsFamily.MAC,
        )
        merged_asset = self.merge_staged_assets(qualys_integration, [staged_host], 1)

        self.assertEqual(merged_asset.merged_data.criticality, AssetCriticality.TIER_2)

    def test_auto_assign_criticality_one_of_two_assets(self):
        integration = IntegrationFactory(organization=self.organization)
        SettingFactory.create(
            category=Setting.Category.ASSET_CRITICALITY,
            key=SettingKey.OS_MAC,
            default_value={"enabled": True, "criticality": AssetCriticality.TIER_1},
        )
        staged_host1 = StagedHostFactory(
            hostname="host1",
            criticality=AssetCriticality.UNKNOWN,
            os__family=OsFamily.MAC,
        )
        staged_host2 = StagedHostFactory(
            hostname="host2",
            criticality=AssetCriticality.TIER_2,
            os__family=OsFamily.MAC,
        )
        merged_assets = self.merge_staged_assets(
            integration, [staged_host1, staged_host2], 2
        )

        host1 = next(h for h in merged_assets if h.merged_data.hostname == "host1")
        host2 = next(h for h in merged_assets if h.merged_data.hostname == "host2")
        self.assert_auto_criticality(host1, AssetCriticality.TIER_1)
        self.assertEqual(host2.merged_data.criticality, AssetCriticality.TIER_2)

    # def test_log_jaccard_similarity_tie(self):
    #     self.set_logging(False)
    #     uuid1 = uuid4()
    #     uuid2 = uuid4()
    #     old_components = {uuid1: {"a", "b"}, uuid2: {"x", "y"}}
    #     new_component = {"x", "a"}
    #     with self.assertLogs(
    #         "apps.assets.services.merging.merge_service", logging.WARNING
    #     ):
    #         merge_service.MergeHelper.best_uuid_for_connected_component(
    #             old_components, new_component
    #         )
    #         self.set_logging(True)

    def test_vulnerability_coverage_mode_changed(self):
        integration = IntegrationFactory(
            organization=self.organization,
            technology_id="qualys_vmpc",
            vulnerability_coverage_mode="ignore",
            endpoint_coverage_mode="ignore",
        )
        staged_host = StagedHostFactory(
            hostname="host1",
            criticality=AssetCriticality.UNKNOWN,
            os__family=OsFamily.MAC,
        )
        merged_asset = self.merge_staged_assets(integration, [staged_host], 1)

        self.assertEqual(
            sorted(merged_asset.coverage_gaps),
            [
                CoverageCategory.ENDPOINT_SECURITY,
                CoverageCategory.VULNERABILITY_MANAGEMENT,
            ],
        )

        # Change the vulnerability coverage mode
        integration.vulnerability_coverage_mode = "enabled"
        integration.save()
        merged_asset = self.merge_staged_assets(integration, [staged_host], 1)

        self.assertEqual(
            merged_asset.coverage_gaps,
            [
                CoverageCategory.ENDPOINT_SECURITY,
            ],
        )

    def test_endpoint_coverage_mode_changed(self):
        integration = IntegrationFactory(
            organization=self.organization,
            technology_id="carbon_black",
            vulnerability_coverage_mode="ignore",
            endpoint_coverage_mode="ignore",
        )
        staged_host = StagedHostFactory(
            hostname="host1",
            criticality=AssetCriticality.UNKNOWN,
            os__family=OsFamily.MAC,
        )
        merged_asset = self.merge_staged_assets(integration, [staged_host], 1)

        self.assertEqual(
            sorted(merged_asset.coverage_gaps),
            [
                CoverageCategory.ENDPOINT_SECURITY,
                CoverageCategory.VULNERABILITY_MANAGEMENT,
            ],
        )

        # Change the endpoint_coverage_mode
        integration.endpoint_coverage_mode = "enabled"
        integration.save()
        merged_asset = self.merge_staged_assets(integration, [staged_host], 1)

        self.assertEqual(
            merged_asset.coverage_gaps,
            [
                CoverageCategory.VULNERABILITY_MANAGEMENT,
            ],
        )

    def test_merge_including_manually_merged(self):
        host1 = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["defender_atp"],
        )

        host2 = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata__integration__technology_id="sentinel_one",
                    attributes__universal_mac_address=["f0:20:ff:5a:75:f8"],
                )
            ],
        )

        manually_merged = merge_service.manually_merge(
            self.organization, [host1.id, host2.id]
        )

        self.assertEqual(manually_merged.source_data.count(), 2)

        integration = IntegrationFactory(organization=self.organization)
        source_to_match = next(host2.source_data.get_sources_by())
        staged_host = StagedHostFactory(
            mac_addresses=source_to_match.attributes.universal_mac_address,
        )

        self.merge_staged_assets(integration, [staged_host], 1)

    def test_merge_manually_merged_existing(self):
        host1 = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["defender_atp"],
        )

        host2 = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata__integration__technology_id="sentinel_one",
                    attributes__universal_mac_address=["f0:20:ff:5a:75:f8"],
                )
            ],
        )

        manually_merged = merge_service.manually_merge(
            self.organization, [host1.id, host2.id]
        )

        self.assertEqual(manually_merged.source_data.count(), 2)
        self.assertEqual(len(list(MergedAsset.documents.search({}))), 1)

        # Create a new merged asset that matches the manually merged asset
        # which is also manually merged.
        integration = IntegrationFactory(
            organization=self.organization, technology_id="falcon_em"
        )
        host3 = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata__integration__id=integration.id,
                    metadata__integration__technology_id="falcon_em",
                    asset_id=hash_dict(
                        {
                            "organization_id": self.organization.id,
                            "technology_id": "falcon_em",
                            "source_id": "falcon_em_source_id",
                        }
                    ),
                    metadata__asset=SourceHostMetadataFactory(
                        source_id="falcon_em_source_id",
                        last_seen=datetime(2021, 1, 1),
                    ),
                )
            ],
            metadata__manually_merged=True,
        )

        self.assertEqual(len(list(MergedAsset.documents.search({}))), 2)

        # Now force the source from host3 to match the other manually merged asset
        # on the universal MAC address.
        source_to_match = next(host2.source_data.get_sources_by())
        staged_host = StagedHostFactory(
            source_id=next(host3.source_data.get_sources_by()).metadata.asset.source_id,
            mac_addresses=source_to_match.attributes.universal_mac_address,
            last_seen=datetime.now(),
        )

        merged_asset = self.merge_staged_assets(integration, [staged_host], 1)
        self.assertTrue(merged_asset.metadata.manually_merged)

    def merge_staged_assets(self, integration, staged_hosts, expected_length):
        merge_service.merge_staged_assets(integration, staged_hosts, "Host")

        # Retrieve and return the merged asset(s)
        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), expected_length)
        if expected_length == 1:
            return merged_assets[0]
        return merged_assets

    def assert_auto_criticality(self, merged_asset, expected_criticality):
        self.assertEqual(merged_asset.merged_data.criticality, expected_criticality)
        self.assertFalse(merged_asset.source_data.has_criticality())

    @staticmethod
    def set_logging(disabled):
        logging.getLogger(
            "apps.assets.services.merging.merge_service"
        ).disabled = disabled
        logging.getLogger("opensearch").disabled = disabled
