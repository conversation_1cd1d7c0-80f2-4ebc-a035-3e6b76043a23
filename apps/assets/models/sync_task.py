import logging

from django.db import models
from django.utils import timezone
from viewflow.fsm import State

from apps.integrations.models import Integration

logger = logging.getLogger(__name__)


class SyncTaskManager(models.Manager):
    def start_task(self, correlation_id, integration_id, asset_type):
        return self.create(
            correlation_id=correlation_id,
            integration_id=integration_id,
            asset_type=asset_type,
            status=self.model.Status.STARTED,
        )

    def set_status(self, correlation_id, status):
        task = self.get(correlation_id=correlation_id)

        flow = TaskFlow(task)
        match status:
            case self.model.Status.STAGED:
                flow.to_staged()
            case self.model.Status.COMPLETED:
                flow.to_completed()
            case self.model.Status.FAILED:
                flow.to_failed()

        return task

    def old_tasks(self):
        """Tasks that are no longer needed and can be deleted"""
        return self.filter(
            status=self.model.Status.COMPLETED,
            finished_at__lt=timezone.now() - timezone.timedelta(days=7),
        )


class SyncTask(models.Model):
    class Status(models.TextChoices):
        STARTED = "started"
        STAGED = "staged"
        COMPLETED = "completed"
        FAILED = "failed"

    correlation_id = models.CharField(max_length=255, unique=True)
    status = models.CharField(choices=Status.choices, max_length=255)
    started_at = models.DateTimeField(auto_now_add=True)
    staged_at = models.DateTimeField(null=True, blank=True)
    finished_at = models.DateTimeField(null=True, blank=True)

    integration = models.ForeignKey(Integration, on_delete=models.CASCADE)
    asset_type = models.CharField(max_length=255)

    objects = SyncTaskManager()

    @property
    def technology_id(self):
        return self.integration.technology_id

    @property
    def organization_id(self):
        return self.integration.organization_id

    @property
    def log_extra(self):
        return {
            "correlation_id": self.correlation_id,
            "integration_id": self.integration_id,
            "org_alias": self.integration.organization.alias,
            "org_id": self.integration.organization.id,
        }

    def __repr__(self):
        return (
            f"SyncTask("
            f"correlation_id={self.correlation_id!r}, "
            f"status={self.status!r}, "
            f"started_at={self.started_at!r}, "
            f"finished_at={self.finished_at!r}, "
            f"integration={self.integration!r}, "
            f"asset_type={self.asset_type!r}"
            f")"
        )


class TaskFlow:
    status = State(SyncTask.Status)

    def __init__(self, task: SyncTask):
        self.task = task

    @status.setter()
    def _set_task_state(self, value):
        self.task.status = value

    @status.getter()
    def _get_task_state(self):
        return self.task.status

    @status.on_success()
    def _on_transition_success(self, descriptor, source, target):
        self.task.save()

    @status.transition(source=SyncTask.Status.STARTED, target=SyncTask.Status.STAGED)
    def to_staged(self):
        self.task.staged_at = timezone.now()

    @status.transition(source=SyncTask.Status.STAGED, target=SyncTask.Status.COMPLETED)
    def to_completed(self):
        self.task.finished_at = timezone.now()

    @status.transition(source=State.ANY, target=SyncTask.Status.FAILED)
    def to_failed(self):
        self.task.finished_at = timezone.now()
