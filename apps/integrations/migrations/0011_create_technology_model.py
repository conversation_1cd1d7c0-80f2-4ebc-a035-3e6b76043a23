# Generated by Django 4.2.20 on 2025-06-17 09:42

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("integrations", "0010_populate_technology_name"),
    ]

    operations = [
        migrations.CreateModel(
            name="Technology",
            fields=[
                (
                    "technology_id",
                    models.CharField(
                        max_length=255, primary_key=True, serialize=False, unique=True
                    ),
                ),
                ("name", models.Char<PERSON>ield(max_length=255)),
                ("category", models.CharField(max_length=255)),
                ("internal", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Technology",
                "verbose_name_plural": "Technologies",
                "db_table": "integrations_technology",
            },
        ),
        migrations.RemoveField(
            model_name="integration",
            name="technology_id",
        ),
        migrations.RemoveField(
            model_name="integration",
            name="technology_name",
        ),
        migrations.AddField(
            model_name="integration",
            name="technology",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.RESTRICT,
                to="integrations.technology",
            ),
        ),
    ]
