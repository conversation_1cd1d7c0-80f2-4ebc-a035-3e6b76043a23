# Generated data migration to populate technology_name field

from django.db import migrations


def populate_technology_names(apps, schema_editor):  # pragma: no cover
    integration = apps.get_model("integrations", "Integration")

    # Technology ID to name mapping from current TechnologyRegistry
    technology_names = {
        "absolute": "Absolute",
        "azure_ad": "Microsoft Entra ID (Azure AD)",
        "carbon_black": "Carbon Black",
        "cb_cloud": "Carbon Black Cloud Endpoint Standard",
        "cb_threat_hunter": "Carbon Black Threat Hunter",
        "cisco_duo": "Duo",
        "cisco_ise": "Cisco Ise AssetView",
        "commvault": "Commvault",
        "cortex_xdr": "Palo Alto Cortex XDR",
        "crowdstrike_falcon": "CrowdStrike Falcon",
        "cyberark_epm": "CyberArk Endpoint Privilege Manager",
        "cylance": "Cylance",
        "defender_atp": "Microsoft Defender for Endpoint",
        "extrahop_revealx_360": "ExtraHop RevealX 360",
        "falcon_em": "CrowdStrike Discover",
        "freshworks_service": "Freshworks Freshservice",
        "import_hosts": "Import Hosts",
        "infoblox_ddi": "Infoblox DDI",
        "ivanti_neurons": "Ivanti Neurons",
        "ivanti_pm": "Ivanti Patch Management",
        "jamf_pro": "JAMF Pro",
        "ms_intune": "Microsoft Intune",
        "netapp_ontap": "NetApp ONTAP",
        "qualys_gav": "Qualys Global AssetView",
        "qualys_vmpc": "Qualys VMDR",
        "rapid7_insightvm": "Rapid7 InsightVM",
        "s1_ranger": "Sentinel One Singularity Ranger",
        "sentinel_one": "Sentinel One",
        "servicenow_cmdb": "ServiceNow CMDB",
        "sevco_io": "Secvo Asset Management",
        "solarwinds_sd": "SolarWinds Service Desk",
        "tanium_em": "Tanium Endpoint Management",
        "tenable_io": "Tenable IO",
        "tm_vision_one": "Trend Micro Vision One",
        "ubiquity_unifi": "Ubiquity Unifi",
        "veeam": "Veeam",
        "veritas_alta_baas": "Veritas Alta BAAS",
        "vmware_aria": "VMware Aria",
        "zscaler": "Zscaler",
        "user_input": "(Internal) User Input",
    }

    for integration in integration.objects.all():
        if integration.technology_id in technology_names:
            integration.technology_name = technology_names[integration.technology_id]
        else:
            # Fallback to technology_id if not found
            integration.technology_name = integration.technology_id
        integration.save()


def reverse_populate_technology_names(apps, schema_editor):  # pragma: no cover
    integration = apps.get_model("integrations", "Integration")
    integration.objects.update(technology_name="")


class Migration(migrations.Migration):
    dependencies = [
        ("integrations", "0009_integration_technology_name"),
    ]

    operations = [
        migrations.RunPython(
            populate_technology_names, reverse_populate_technology_names
        ),
    ]
