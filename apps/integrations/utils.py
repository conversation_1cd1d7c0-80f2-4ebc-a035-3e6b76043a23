"""
Utility functions for technology information retrieval from database.
"""
from apps.integrations.models import Integration


def get_technology_name(technology_id: str) -> str:
    """
    Get technology name by technology_id from the database.
    Falls back to technology_id if not found.
    """
    try:
        integration = (
            Integration.objects.filter(technology_id=technology_id)
            .values_list("technology_name", flat=True)
            .first()
        )
        return integration or technology_id
    except Exception:
        return technology_id


def get_external_technology_ids() -> list[str]:
    """
    Get list of external (enabled) technology IDs from the database.
    Excludes demo_environment as it's an internal technology.
    """
    try:
        tech_ids = list(
            Integration.objects.filter(enabled=True)
            .exclude(technology_id="demo_environment")
            .values_list("technology_id", flat=True)
            .distinct()
        )
        # Fallback if database query succeeds but returns empty list
        if not tech_ids:
            return [
                "crowdstrike_falcon",
                "tenable_io",
                "sentinel_one",
                "defender_atp",
                "azure_ad",
                "ms_intune",
                "qualys_vmpc",
                "carbon_black",
            ]
        return tech_ids
    except Exception:
        # Fallback for tests or when database is not available
        return [
            "crowdstrike_falcon",
            "tenable_io",
            "sentinel_one",
            "defender_atp",
            "azure_ad",
            "ms_intune",
            "qualys_vmpc",
            "carbon_black",
        ]


def get_all_technology_ids() -> list[str]:
    """
    Get list of all technology IDs from the database.
    """
    tech_ids = list(
        Integration.objects.values_list("technology_id", flat=True).distinct()
    )
    # Fallback if database query succeeds but returns empty list
    if not tech_ids:
        return [
            "crowdstrike_falcon",
            "tenable_io",
            "sentinel_one",
            "defender_atp",
            "azure_ad",
            "ms_intune",
            "qualys_vmpc",
            "carbon_black",
            "user_input",
        ]
    return tech_ids


def get_technology_category(technology_id: str) -> str:
    """
    Get the category for a technology ID based on the original Technology mapping.
    """
    # Technology ID to category mapping from original TechnologyRegistry
    technology_categories = {
        "absolute": "endpoint_security",
        "azure_ad": "asset_source",
        "carbon_black": "endpoint_security",
        "cb_cloud": "endpoint_security",
        "cb_threat_hunter": "endpoint_security",
        "cisco_duo": "asset_source",
        "cisco_ise": "asset_source",
        "commvault": "backup_agent",
        "cortex_xdr": "endpoint_security",
        "crowdstrike_falcon": "endpoint_security",
        "cyberark_epm": "endpoint_security",
        "cylance": "endpoint_security",
        "defender_atp": "endpoint_security",
        "extrahop_revealx_360": "asset_source",
        "falcon_em": "asset_source",
        "freshworks_service": "asset_source",
        "import_hosts": "asset_source",
        "infoblox_ddi": "asset_source",
        "ivanti_neurons": "asset_source",
        "ivanti_pm": "asset_source",
        "jamf_pro": "endpoint_security",
        "ms_intune": "endpoint_security",
        "netapp_ontap": "asset_source",
        "qualys_gav": "asset_source",
        "qualys_vmpc": "vulnerability_management",
        "rapid7_insightvm": "vulnerability_management",
        "s1_ranger": "asset_source",
        "sentinel_one": "endpoint_security",
        "servicenow_cmdb": "asset_source",
        "sevco_io": "asset_source",
        "solarwinds_sd": "asset_source",
        "tanium_em": "endpoint_security",
        "tenable_io": "vulnerability_management",
        "tm_vision_one": "endpoint_security",
        "ubiquity_unifi": "asset_source",
        "veeam": "backup_agent",
        "veritas_alta_baas": "backup_agent",
        "vmware_aria": "asset_source",
        "zscaler": "asset_source",
        "user_input": "asset_source",
        "demo_environment": "asset_source",
    }

    return technology_categories.get(technology_id, "asset_source")
