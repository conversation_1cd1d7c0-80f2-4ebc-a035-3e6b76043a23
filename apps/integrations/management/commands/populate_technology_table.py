"""
Management command to populate Technology table from data connectors API
"""
from django.core.management.base import BaseCommand
from django.db import transaction

from apps.integrations.api.data_connectors import data_connectors_api
from apps.integrations.models import Technology
from apps.integrations.technology import TechnologyRegistry


class Command(BaseCommand):
    help = 'Populate Technology table from data connectors API and registry'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            dest='dry_run',
            help='Run without making changes to the database',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        if dry_run:
            self.stdout.write("Running in dry-run mode. No changes will be made.")
        
        # Get technologies from the registry
        registry_technologies = {}
        for tech_enum in TechnologyRegistry:
            tech = tech_enum.value
            registry_technologies[tech.id] = {
                'name': tech.name,
                'category': tech.category,
                'internal': tech.internal
            }
        
        # Try to get technologies from data connectors API
        api_technologies = {}
        try:
            integrations = data_connectors_api.get_integrations()
            for integration in integrations:
                tech_id = integration.technology_id
                if tech_id not in api_technologies:
                    api_technologies[tech_id] = {
                        'name': integration.technology_name,
                        'category': integration.category_id,
                        'internal': False
                    }
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f"Failed to fetch from data connectors API: {e}")
            )
            self.stdout.write("Using only registry data.")
        
        # Merge data - registry takes precedence
        all_technologies = {}
        all_technologies.update(api_technologies)
        all_technologies.update(registry_technologies)
        
        created_count = 0
        updated_count = 0
        
        with transaction.atomic():
            for tech_id, tech_data in all_technologies.items():
                if dry_run:
                    existing = Technology.objects.filter(technology_id=tech_id).first()
                    if existing:
                        self.stdout.write(f"Would update: {tech_id} -> {tech_data['name']}")
                    else:
                        self.stdout.write(f"Would create: {tech_id} -> {tech_data['name']}")
                    continue
                
                technology, created = Technology.objects.update_or_create(
                    technology_id=tech_id,
                    defaults={
                        'name': tech_data['name'],
                        'category': tech_data['category'],
                        'internal': tech_data['internal']
                    }
                )
                
                if created:
                    created_count += 1
                    self.stdout.write(
                        self.style.SUCCESS(f"Created: {technology}")
                    )
                else:
                    updated_count += 1
                    self.stdout.write(
                        self.style.SUCCESS(f"Updated: {technology}")
                    )
        
        if not dry_run:
            self.stdout.write(
                self.style.SUCCESS(
                    f"Successfully populated Technology table. "
                    f"Created: {created_count}, Updated: {updated_count}"
                )
            )
        else:
            self.stdout.write("Dry run completed. No changes made.")
